import 'package:opti_tickets/src/core/consts/network/api_endpoints.dart';
import 'package:opti_tickets/src/core/shared/models/product_model.dart';
import 'package:xr_helper/xr_helper.dart';

class ProductRepository with BaseRepository {
  final BaseApiServices networkApiService;

  ProductRepository({
    required this.networkApiService,
  });

  // * Get Products List
  Future<ProductListModel> getProductsList({int? clientId}) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.productsList;

        final response = await networkApiService.postResponse(url, body: {
          'cl_id': clientId ?? 0,
        });

        if (response == null) {
          return ProductListModel.empty();
        }

        final productListModel = ProductListModel.fromJson(response);

        return productListModel;
      },
    );
  }
}
