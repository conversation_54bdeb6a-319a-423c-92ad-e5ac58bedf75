import 'package:opti_tickets/src/core/shared/models/product_model.dart';
import 'package:opti_tickets/src/core/shared/repositories/product_repository.dart';
import 'package:xr_helper/xr_helper.dart';

class ProductController extends BaseVM {
  final ProductRepository productRepo;

  ProductController({
    required this.productRepo,
  });

  // * Get Products List
  Future<ProductListModel> getProductsList() async {
    return await baseFunction(
      () async {
        return await productRepo.getProductsList();
      },
    );
  }
}
