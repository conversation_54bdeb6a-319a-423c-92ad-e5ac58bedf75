import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/shared/models/product_model.dart';
import 'package:opti_tickets/src/core/shared/providers/product_providers.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:xr_helper/xr_helper.dart';

class ProductMultiSelectSheet extends HookConsumerWidget {
  final List<SelectedProductDetail> selectedProducts;
  final Function(List<SelectedProductDetail>) onChanged;
  final String? label;
  final int? clientId;

  const ProductMultiSelectSheet({
    super.key,
    required this.selectedProducts,
    required this.onChanged,
    this.label,
    this.clientId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return GestureDetector(
      onTap: () => _showProductSelectionSheet(context, ref),
      child: Container(
        padding: const EdgeInsets.all(AppSpaces.padding16),
        decoration: BoxDecoration(
          border: Border.all(color: ColorManager.lightGrey),
          borderRadius: BorderRadius.circular(AppRadius.radius12),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.inventory,
              color: ColorManager.primaryColor,
            ),
            AppGaps.gap12,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    label ?? context.tr.selectProduct,
                    style: AppTextStyles.labelMedium.copyWith(
                      color: ColorManager.darkGrey,
                    ),
                  ),
                  if (selectedProducts.isNotEmpty) ...[
                    AppGaps.gap4,
                    Text(
                      '${selectedProducts.length} ${selectedProducts.length == 1 ? context.tr.productSelected : context.tr.productsSelected}',
                      style: AppTextStyles.body.copyWith(
                        color: ColorManager.primaryColor,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            const Icon(
              Icons.arrow_drop_down,
              color: ColorManager.darkGrey,
            ),
          ],
        ),
      ),
    );
  }

  void _showProductSelectionSheet(BuildContext context, WidgetRef ref) {
    if (clientId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(context.tr.pleaseSelectClientFirst)),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _ProductSelectionBottomSheet(
        selectedProducts: selectedProducts,
        onChanged: onChanged,
        clientId: clientId!,
      ),
    );
  }
}

class _ProductSelectionBottomSheet extends HookConsumerWidget {
  final List<SelectedProductDetail> selectedProducts;
  final Function(List<SelectedProductDetail>) onChanged;
  final int clientId;

  const _ProductSelectionBottomSheet({
    required this.selectedProducts,
    required this.onChanged,
    required this.clientId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final productsAsyncValue = ref.watch(getProductsListFutureProvider);
    final selectedProductsState = useState<List<SelectedProductDetail>>(
      List.from(selectedProducts),
    );

    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppRadius.radius24),
          topRight: Radius.circular(AppRadius.radius24),
        ),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: AppSpaces.padding12),
            decoration: BoxDecoration(
              color: ColorManager.lightGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: AppSpaces.padding16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  context.tr.selectProductsAndServices,
                  style: AppTextStyles.title.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    onChanged(selectedProductsState.value);
                    Navigator.pop(context);
                  },
                  child: Text(
                    context.tr.done,
                    style: AppTextStyles.labelMedium.copyWith(
                      color: ColorManager.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const Divider(),

          // Content
          Expanded(
            child: Consumer(
              builder: (context, ref, child) {
                final productsAsyncValue = ref.watch(getProductsListByClientFutureProvider(clientId));
                return productsAsyncValue.when(
              data: (productListModel) {
                if (productListModel.data.isEmpty) {
                  return Center(
                    child: Text(
                      context.tr.noProductsAvailable,
                      style: AppTextStyles.body.copyWith(
                        color: ColorManager.lightGrey,
                      ),
                    ),
                  );
                }

                final products = productListModel.data.first.categoryLists;

                return ListView.builder(
                  padding: const EdgeInsets.all(AppSpaces.padding16),
                  itemCount: products.length,
                  itemBuilder: (context, index) {
                    final product = products[index];
                    final isSelected = selectedProductsState.value.any(
                        (selected) =>
                            selected.product.productId == product.productId);

                    return _ProductTile(
                      product: product,
                      isSelected: isSelected,
                      selectedProductDetail:
                          selectedProductsState.value.firstWhere(
                        (selected) =>
                            selected.product.productId == product.productId,
                        orElse: () => SelectedProductDetail(product: product),
                      ),
                      onChanged: (selectedDetail) {
                        final updatedList = List<SelectedProductDetail>.from(
                          selectedProductsState.value,
                        );

                        if (selectedDetail == null) {
                          // Remove product
                          updatedList.removeWhere(
                            (item) =>
                                item.product.productId == product.productId,
                          );
                        } else {
                          // Add or update product
                          final existingIndex = updatedList.indexWhere(
                            (item) =>
                                item.product.productId == product.productId,
                          );

                          if (existingIndex >= 0) {
                            updatedList[existingIndex] = selectedDetail;
                          } else {
                            updatedList.add(selectedDetail);
                          }
                        }

                        selectedProductsState.value = updatedList;
                      },
                    );
                  },
                );
              },
              loading: () => const Center(
                child: CircularProgressIndicator(),
              ),
              error: (error, stackTrace) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.error_outline,
                      size: 64,
                      color: ColorManager.errorColor,
                    ),
                    AppGaps.gap16,
                    Text(
                      context.tr.failedToLoadProducts,
                      style: AppTextStyles.body.copyWith(
                        color: ColorManager.errorColor,
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _ProductTile extends HookWidget {
  final Product product;
  final bool isSelected;
  final SelectedProductDetail selectedProductDetail;
  final Function(SelectedProductDetail?) onChanged;

  const _ProductTile({
    required this.product,
    required this.isSelected,
    required this.selectedProductDetail,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final isExpanded = useState(false);
    final selectedSubProducts = useState<List<SubProduct>>(
      selectedProductDetail.selectedSubProducts,
    );
    final hasLimitUsers = useState<bool>(
      selectedProductDetail.hasLimitUsers,
    );

    return Card(
      margin: const EdgeInsets.only(bottom: AppSpaces.padding8),
      child: Column(
        children: [
          ListTile(
            leading: Icon(
              isSelected ? Icons.check_circle : Icons.circle,
              color: isSelected ? ColorManager.primaryColor : Colors.grey,
            ),
            title: Text(
              product.productName,
              style: AppTextStyles.body.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            trailing: product.hasSubProducts
                ? Icon(
                    isExpanded.value ? Icons.expand_less : Icons.expand_more,
                    color: ColorManager.darkGrey,
                  )
                : null,
            onTap: () {
              if (isSelected) {
                onChanged(null);
              } else {
                if (product.hasSubProducts) {
                  isExpanded.value = !isExpanded.value;
                } else {
                  onChanged(SelectedProductDetail(
                    product: product,
                    hasLimitUsers: product.hasUserLimit,
                  ));
                }
              }
            },
          ),
          if (isSelected && product.hasSubProducts && isExpanded.value) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(AppSpaces.padding16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${context.tr.selectSubServices}:',
                    style: AppTextStyles.labelMedium.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  AppGaps.gap8,
                  ...product.subProducts.map((subProduct) {
                    final isSubSelected = selectedSubProducts.value.any(
                        (sub) => sub.subProductId == subProduct.subProductId);

                    return CheckboxListTile(
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                      title: Text(
                        subProduct.subProductName,
                        style: AppTextStyles.body,
                      ),
                      subtitle: Text(
                        '${context.tr.cost}: ${subProduct.subProductCost}',
                        style: AppTextStyles.labelSmall.copyWith(
                          color: ColorManager.darkGrey,
                        ),
                      ),
                      value: isSubSelected,
                      onChanged: (value) {
                        final updatedSubProducts = List<SubProduct>.from(
                          selectedSubProducts.value,
                        );

                        if (value == true) {
                          updatedSubProducts.add(subProduct);
                        } else {
                          updatedSubProducts.removeWhere(
                            (sub) =>
                                sub.subProductId == subProduct.subProductId,
                          );
                        }

                        selectedSubProducts.value = updatedSubProducts;

                        onChanged(SelectedProductDetail(
                          product: product,
                          selectedSubProducts: updatedSubProducts,
                          hasLimitUsers: hasLimitUsers.value,
                        ));
                      },
                    );
                  }).toList(),
                  if (product.hasUserLimit) ...[
                    AppGaps.gap8,
                    CheckboxListTile(
                      dense: true,
                      contentPadding: EdgeInsets.zero,
                      title: Text(
                        context.tr.hasUserLimit,
                        style: AppTextStyles.body,
                      ),
                      value: hasLimitUsers.value,
                      onChanged: (value) {
                        hasLimitUsers.value = value ?? false;
                        onChanged(SelectedProductDetail(
                          product: product,
                          selectedSubProducts: selectedSubProducts.value,
                          hasLimitUsers: hasLimitUsers.value,
                        ));
                      },
                    ),
                  ],
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
