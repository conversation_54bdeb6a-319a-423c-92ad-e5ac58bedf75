import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';

import '../../../theme/color_manager.dart';
import 'controller/bottom_nav_bar.controller.dart';

class BottomNavBarWidget extends ConsumerWidget {
  const BottomNavBarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedIndex = ref.watch(bottomNavigationControllerProvider);

    return BottomNavigationBar(
      currentIndex: selectedIndex,
      type: BottomNavigationBarType.fixed,
      selectedItemColor: ColorManager.primaryColor,
      unselectedItemColor: ColorManager.lightGrey,
      backgroundColor: Colors.white,
      elevation: 8,
      items: [
        BottomNavigationBarItem(
          icon: const Icon(CupertinoIcons.home),
          label: context.tr.home,
        ),
        BottomNavigationBarItem(
          icon: const Icon(FontAwesomeIcons.calendarDays),
          label: context.tr.leaves,
        ),
        BottomNavigationBarItem(
          icon: const Icon(FontAwesomeIcons.users),
          label: context.tr.clients,
        ),
      ],
      onTap: (index) {
        ref
            .read(bottomNavigationControllerProvider.notifier)
            .changeIndex(index);
      },
    );
  }
}
