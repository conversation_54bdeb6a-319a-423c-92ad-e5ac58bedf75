import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../controllers/product_controller.dart';
import '../models/product_model.dart';
import '../repositories/product_repository.dart';
import 'network_api_service_provider.dart';

// * Product Repo Provider ========================================
final productRepoProvider = Provider<ProductRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return ProductRepository(networkApiService: networkApiService);
});

// * Product Controller Provider ========================================
final productControllerProvider = Provider<ProductController>(
  (ref) {
    final productRepo = ref.watch(productRepoProvider);

    return ProductController(
      productRepo: productRepo,
    );
  },
);

// * Get Products List Future Provider ========================================
final getProductsListFutureProvider =
    FutureProvider.autoDispose<ProductListModel>((ref) async {
  final productController = ref.watch(productControllerProvider);

  return await productController.getProductsList();
});
