import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/quotation_controller.dart';
import '../models/quotation_model.dart';
import '../repositories/quotation_repository.dart';

// * Quotation Repo Provider ========================================
final quotationRepoProvider = Provider<QuotationRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return QuotationRepository(networkApiService: networkApiService);
});

// * Quotation Controller Provider ========================================
final quotationControllerProvider = Provider<QuotationController>(
  (ref) {
    final quotationRepo = ref.watch(quotationRepoProvider);

    return QuotationController(
      quotationRepo: quotationRepo,
    );
  },
);

// * Get Quotation List Future Provider ========================================
final getQuotationListFutureProvider =
    FutureProvider.autoDispose<QuotationListModel>((ref) async {
  final quotationController = ref.watch(quotationControllerProvider);

  return await quotationController.getQuotationList();
});
