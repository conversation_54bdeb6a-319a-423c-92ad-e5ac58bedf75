import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
import 'package:opti_tickets/src/screens/quotations/providers/quotation_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class AddQuotationScreen extends ConsumerStatefulWidget {
  const AddQuotationScreen({super.key});

  @override
  ConsumerState<AddQuotationScreen> createState() => _AddQuotationScreenState();
}

class _AddQuotationScreenState extends ConsumerState<AddQuotationScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _clientIdController;
  late TextEditingController _validityDaysController;

  late DateTime _startTime;
  late String _startDate;
  late String _startTimeString;

  // Selected products for multi-selection
  List<SelectedProductDetail> _selectedProducts = [];

  @override
  void initState() {
    super.initState();
    _clientIdController = TextEditingController();
    _validityDaysController = TextEditingController(text: '7');

    // Record start time
    _startTime = DateTime.now();
    _startDate = DateFormat('yyyy-MM-dd').format(_startTime);
    _startTimeString = DateFormat('HH:mm').format(_startTime);
  }

  @override
  void dispose() {
    _clientIdController.dispose();
    _validityDaysController.dispose();
    super.dispose();
  }

  Future<void> _submitQuotation() async {
    if (!_formKey.currentState!.validate()) return;

    // Validate that at least one product is selected
    if (_selectedProducts.isEmpty) {
      context.showSnackBar(context.tr.pleaseSelectAtLeastOneProduct);
      return;
    }

    // Record finish time
    final finishTime = DateTime.now();
    final finishDate = DateFormat('yyyy-MM-dd').format(finishTime);
    final finishTimeString = DateFormat('HH:mm').format(finishTime);

    // Convert selected products to quotation details
    final List<QuotationDetail> qDetails = [];
    for (final selectedProduct in _selectedProducts) {
      if (selectedProduct.product.hasSubProducts &&
          selectedProduct.selectedSubProducts.isNotEmpty) {
        // Add each selected sub-product as a separate detail
        for (final subProduct in selectedProduct.selectedSubProducts) {
          qDetails.add(QuotationDetail(
            productId: selectedProduct.product.productId,
            subProductId: subProduct.subProductId,
            hasLimitUsers: selectedProduct.hasLimitUsers,
          ));
        }
      } else {
        // Add the main product
        qDetails.add(QuotationDetail(
          productId: selectedProduct.product.productId,
          subProductId: 0, // No sub-product selected
          hasLimitUsers: selectedProduct.hasLimitUsers,
        ));
      }
    }

    final quotationData = AddQuotationModel(
      qInfo: QuotationInfo(
        clientID: _clientIdController.text,
        createdDate: _startDate,
        validityDays: _validityDaysController.text,
      ),
      qDetails: qDetails,
      task: QuotationTask(
        startDate: _startDate,
        startTime: _startTimeString,
        finishDate: finishDate,
        finishTime: finishTimeString,
      ),
    );

    final quotationController = ref.read(quotationControllerProvider);

    final success =
        await quotationController.addQuotation(quotationData: quotationData);

    if (success && mounted) {
      Navigator.pop(context);
      context.showBarMessage(context.tr.quotationSaved);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: Text(
          context.tr.saveQuotation,
          style: AppTextStyles.title,
        ),
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_new),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              // Container(
              //   width: double.infinity,
              //   padding: const EdgeInsets.all(AppSpaces.padding16),
              //   decoration: BoxDecoration(
              //     color: ColorManager.primaryColor.withOpacity(0.1),
              //     borderRadius: BorderRadius.circular(AppRadius.radius12),
              //   ),
              //   child: Column(
              //     children: [
              //       const Icon(
              //         Icons.description,
              //         size: 48,
              //         color: ColorManager.primaryColor,
              //       ),
              //       AppGaps.gap8,
              //       Text(
              //         context.tr.saveQuotation,
              //         style: AppTextStyles.labelMedium.copyWith(
              //           color: ColorManager.primaryColor,
              //           fontWeight: FontWeight.w600,
              //         ),
              //       ),
              //     ],
              //   ),
              // ),
              // AppGaps.gap24,

              // Client ID
              TextFormField(
                controller: _clientIdController,
                decoration: InputDecoration(
                  labelText: context.tr.clientID,
                  prefixIcon: const Icon(Icons.business),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Client ID cannot be empty';
                  }
                  return null;
                },
              ),
              AppGaps.gap16,

              // Validity Days
              TextFormField(
                controller: _validityDaysController,
                decoration: InputDecoration(
                  labelText: context.tr.validityDays,
                  prefixIcon: const Icon(Icons.calendar_today),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Validity days cannot be empty';
                  }
                  return null;
                },
              ),
              AppGaps.gap16,

              // Product Multi-Selection
              ProductMultiSelectSheet(
                selectedProducts: _selectedProducts,
                onChanged: (selectedProducts) {
                  setState(() {
                    _selectedProducts = selectedProducts;
                  });
                },
                label: context.tr.selectProduct,
              ),
              AppGaps.gap48,

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _submitQuotation,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorManager.primaryColor,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                    ),
                  ),
                  child: Text(
                    context.tr.submit,
                    style: AppTextStyles.subTitle.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
