import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
import 'package:opti_tickets/src/screens/quotations/providers/quotation_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class AddQuotationScreen extends ConsumerStatefulWidget {
  const AddQuotationScreen({super.key});

  @override
  ConsumerState<AddQuotationScreen> createState() => _AddQuotationScreenState();
}

class _AddQuotationScreenState extends ConsumerState<AddQuotationScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _clientIdController;
  late TextEditingController _validityDaysController;

  late DateTime _startTime;
  late String _startDate;
  late String _startTimeString;

  // Static data for products (will be replaced with dynamic data later)
  int _selectedProductId = 1;
  int _selectedSubProductId = 1;
  bool _hasLimitUsers = false;

  final List<Map<String, dynamic>> _products = [
    {'id': 1, 'name': 'نظام لافـــــا'},
    {'id': 2, 'name': 'نظام إدارة المحتوى'},
    {'id': 3, 'name': 'نظام إدارة العملاء'},
  ];

  final List<Map<String, dynamic>> _subProducts = [
    {'id': 1, 'name': 'الحسابات العامة'},
    {'id': 2, 'name': 'إدارة المخزون'},
    {'id': 3, 'name': 'إدارة الموارد البشرية'},
  ];

  @override
  void initState() {
    super.initState();
    _clientIdController = TextEditingController();
    _validityDaysController = TextEditingController(text: '7');

    // Record start time
    _startTime = DateTime.now();
    _startDate = DateFormat('yyyy-MM-dd').format(_startTime);
    _startTimeString = DateFormat('HH:mm').format(_startTime);
  }

  @override
  void dispose() {
    _clientIdController.dispose();
    _validityDaysController.dispose();
    super.dispose();
  }

  Future<void> _submitQuotation() async {
    if (!_formKey.currentState!.validate()) return;

    // Record finish time
    final finishTime = DateTime.now();
    final finishDate = DateFormat('yyyy-MM-dd').format(finishTime);
    final finishTimeString = DateFormat('HH:mm').format(finishTime);

    final quotationData = AddQuotationModel(
      qInfo: QuotationInfo(
        clientID: _clientIdController.text,
        createdDate: _startDate,
        validityDays: _validityDaysController.text,
      ),
      qDetails: [
        QuotationDetail(
          productId: _selectedProductId,
          subProductId: _selectedSubProductId,
          hasLimitUsers: _hasLimitUsers,
        ),
      ],
      task: QuotationTask(
        startDate: _startDate,
        startTime: _startTimeString,
        finishDate: finishDate,
        finishTime: finishTimeString,
      ),
    );

    final quotationController = ref.read(quotationControllerProvider);

    final success =
        await quotationController.addQuotation(quotationData: quotationData);

    if (success && mounted) {
      Navigator.pop(context);
      context.showBarMessage(context.tr.quotationSaved);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: Text(
          context.tr.saveQuotation,
          style: AppTextStyles.title,
        ),
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_new),
        ),
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppSpaces.padding16),
                decoration: BoxDecoration(
                  color: ColorManager.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.radius12),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.description,
                      size: 48,
                      color: ColorManager.primaryColor,
                    ),
                    AppGaps.gap8,
                    Text(
                      context.tr.saveQuotation,
                      style: AppTextStyles.labelMedium.copyWith(
                        color: ColorManager.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
              AppGaps.gap24,

              // Client ID
              TextFormField(
                controller: _clientIdController,
                decoration: InputDecoration(
                  labelText: context.tr.clientID,
                  prefixIcon: const Icon(Icons.business),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Client ID cannot be empty';
                  }
                  return null;
                },
              ),
              AppGaps.gap16,

              // Validity Days
              TextFormField(
                controller: _validityDaysController,
                decoration: InputDecoration(
                  labelText: context.tr.validityDays,
                  prefixIcon: const Icon(Icons.calendar_today),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Validity days cannot be empty';
                  }
                  return null;
                },
              ),
              AppGaps.gap16,

              // Product Selection
              DropdownButtonFormField<int>(
                value: _selectedProductId,
                decoration: InputDecoration(
                  labelText: context.tr.selectProduct,
                  prefixIcon: const Icon(Icons.inventory),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                ),
                items: _products.map((product) {
                  return DropdownMenuItem<int>(
                    value: product['id'],
                    child: Text(product['name']),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedProductId = value;
                    });
                  }
                },
              ),
              AppGaps.gap16,

              // Sub Product Selection
              DropdownButtonFormField<int>(
                value: _selectedSubProductId,
                decoration: InputDecoration(
                  labelText: context.tr.selectSubProduct,
                  prefixIcon: const Icon(Icons.category),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                ),
                items: _subProducts.map((subProduct) {
                  return DropdownMenuItem<int>(
                    value: subProduct['id'],
                    child: Text(subProduct['name']),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedSubProductId = value;
                    });
                  }
                },
              ),
              AppGaps.gap16,

              // Has Limit Users Checkbox
              CheckboxListTile(
                title: Text(context.tr.hasLimitUsers),
                value: _hasLimitUsers,
                onChanged: (value) {
                  setState(() {
                    _hasLimitUsers = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
              AppGaps.gap48,

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _submitQuotation,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorManager.primaryColor,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                    ),
                  ),
                  child: Text(
                    context.tr.submit,
                    style: AppTextStyles.subTitle.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
