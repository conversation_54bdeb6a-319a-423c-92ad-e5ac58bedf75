import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/quotations/models/quotation_model.dart';
import 'package:opti_tickets/src/screens/quotations/providers/quotation_providers.dart';
import 'package:opti_tickets/src/screens/quotations/view/add_quotation_screen.dart';
import 'package:opti_tickets/src/screens/quotations/view/widgets/quotation_card_widget.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:xr_helper/xr_helper.dart';

class QuotationsScreen extends ConsumerWidget {
  const QuotationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final quotationAsyncValue = ref.watch(getQuotationListFutureProvider);

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: Text(
          context.tr.quotationsList,
          style: AppTextStyles.title,
        ),
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: [
          quotationAsyncValue.when(
            data: (quotationModel) {
              if (quotationModel.can.add) {
                return IconButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const AddQuotationScreen(),
                      ),
                    ).then((_) {
                      // Refresh the list when returning from add screen
                      ref.invalidate(getQuotationListFutureProvider);
                    });
                  },
                  icon: const Icon(Icons.add),
                );
              }
              return const SizedBox.shrink();
            },
            loading: () => const SizedBox.shrink(),
            error: (_, __) => const SizedBox.shrink(),
          ),
        ],
      ),
      body: quotationAsyncValue.when(
        data: (quotationModel) {
          if (quotationModel.quotations.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.description_outlined,
                    size: 64,
                    color: ColorManager.lightGrey,
                  ),
                  AppGaps.gap16,
                  Text(
                    context.tr.noDataFound,
                    style: AppTextStyles.body.copyWith(
                      color: ColorManager.lightGrey,
                    ),
                  ),
                ],
              ),
            );
          }

          return Padding(
            padding: const EdgeInsets.all(AppSpaces.padding16),
            child: ListView.separated(
              itemCount: quotationModel.quotations.length,
              separatorBuilder: (context, index) => AppGaps.gap12,
              itemBuilder: (context, index) {
                final quotation = quotationModel.quotations[index];
                return QuotationCardWidget(quotation: quotation);
              },
            ),
          );
        },
        loading: () => Padding(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          child: Skeletonizer(
            enabled: true,
            child: ListView.separated(
              itemCount: 5,
              separatorBuilder: (context, index) => AppGaps.gap12,
              itemBuilder: (context, index) {
                return QuotationCardWidget(
                  quotation: Quotation(
                    unicode: 'Loading...',
                    client: 'Loading client name...',
                    createat: '2024-01-01 00:00:00',
                    status: QuotationStatus(
                      status: 'Loading...',
                      color: 'info',
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        error: (error, stackTrace) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: ColorManager.errorColor,
              ),
              AppGaps.gap16,
              Text(
                context.tr.somethingWentWrong,
                style: AppTextStyles.body.copyWith(
                  color: ColorManager.errorColor,
                ),
              ),
              AppGaps.gap16,
              ElevatedButton(
                onPressed: () {
                  ref.invalidate(getQuotationListFutureProvider);
                },
                child: Text(context.tr.retry),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
