import 'package:equatable/equatable.dart';

class QuotationListModel extends Equatable {
  final bool success;
  final QuotationPermissions can;
  final List<Quotation> quotations;

  const QuotationListModel({
    this.success = false,
    this.can = const QuotationPermissions(),
    this.quotations = const [],
  });

  factory QuotationListModel.fromJson(Map<String, dynamic> json) {
    return QuotationListModel(
      success: json['success'] ?? false,
      can: QuotationPermissions.fromJson(json['can'] ?? {}),
      quotations: (json['dt'] as List<dynamic>?)
              ?.map((e) => Quotation.fromJson(e))
              .toList() ??
          [],
    );
  }

  factory QuotationListModel.empty() => const QuotationListModel();

  @override
  List<Object?> get props => [success, can, quotations];
}

class QuotationPermissions extends Equatable {
  final bool add;

  const QuotationPermissions({
    this.add = false,
  });

  factory QuotationPermissions.from<PERSON>son(Map<String, dynamic> json) {
    return QuotationPermissions(
      add: json['add'] ?? false,
    );
  }

  @override
  List<Object?> get props => [add];
}

class Quotation extends Equatable {
  final int ipd;
  final String unicode;
  final String createat;
  final String client;
  final Map<String, List<QuotationSubscription>> subscriptions;
  final QuotationStatus status;

  const Quotation({
    this.ipd = 0,
    this.unicode = '',
    this.createat = '',
    this.client = '',
    this.subscriptions = const {},
    this.status = const QuotationStatus(),
  });

  factory Quotation.fromJson(Map<String, dynamic> json) {
    Map<String, List<QuotationSubscription>> subscriptionsMap = {};
    
    if (json['subscriptions'] != null) {
      (json['subscriptions'] as Map<String, dynamic>).forEach((key, value) {
        subscriptionsMap[key] = (value as List<dynamic>)
            .map((e) => QuotationSubscription.fromJson(e))
            .toList();
      });
    }

    return Quotation(
      ipd: json['ipd'] ?? 0,
      unicode: json['unicode'] ?? '',
      createat: json['createat'] ?? '',
      client: json['client'] ?? '',
      subscriptions: subscriptionsMap,
      status: QuotationStatus.fromJson(json['status'] ?? {}),
    );
  }

  @override
  List<Object?> get props => [ipd, unicode, createat, client, subscriptions, status];
}

class QuotationSubscription extends Equatable {
  final String serviceName;
  final String subServiceName;

  const QuotationSubscription({
    this.serviceName = '',
    this.subServiceName = '',
  });

  factory QuotationSubscription.fromJson(Map<String, dynamic> json) {
    return QuotationSubscription(
      serviceName: json['service_name'] ?? '',
      subServiceName: json['subservice_name'] ?? '',
    );
  }

  @override
  List<Object?> get props => [serviceName, subServiceName];
}

class QuotationStatus extends Equatable {
  final int id;
  final String status;
  final String color;
  final String? message;

  const QuotationStatus({
    this.id = 0,
    this.status = '',
    this.color = '',
    this.message,
  });

  factory QuotationStatus.fromJson(Map<String, dynamic> json) {
    return QuotationStatus(
      id: json['id'] ?? 0,
      status: json['status'] ?? '',
      color: json['color'] ?? '',
      message: json['message'],
    );
  }

  @override
  List<Object?> get props => [id, status, color, message];
}

class AddQuotationModel extends Equatable {
  final QuotationInfo qInfo;
  final List<QuotationDetail> qDetails;
  final QuotationTask task;

  const AddQuotationModel({
    this.qInfo = const QuotationInfo(),
    this.qDetails = const [],
    this.task = const QuotationTask(),
  });

  Map<String, dynamic> toJson() {
    return {
      'q_info': qInfo.toJson(),
      'q_details': qDetails.map((e) => e.toJson()).toList(),
      'task': task.toJson(),
    };
  }

  @override
  List<Object?> get props => [qInfo, qDetails, task];
}

class QuotationInfo extends Equatable {
  final String clientID;
  final String createdDate;
  final String validityDays;

  const QuotationInfo({
    this.clientID = '',
    this.createdDate = '',
    this.validityDays = '',
  });

  Map<String, dynamic> toJson() {
    return {
      'clientID': clientID,
      'created_date': createdDate,
      'validity_days': validityDays,
    };
  }

  @override
  List<Object?> get props => [clientID, createdDate, validityDays];
}

class QuotationDetail extends Equatable {
  final int productId;
  final int subProductId;
  final bool hasLimitUsers;

  const QuotationDetail({
    this.productId = 0,
    this.subProductId = 0,
    this.hasLimitUsers = false,
  });

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'subProductId': subProductId,
      'hasLimitUsers': hasLimitUsers,
    };
  }

  @override
  List<Object?> get props => [productId, subProductId, hasLimitUsers];
}

class QuotationTask extends Equatable {
  final String startDate;
  final String startTime;
  final String finishDate;
  final String finishTime;

  const QuotationTask({
    this.startDate = '',
    this.startTime = '',
    this.finishDate = '',
    this.finishTime = '',
  });

  Map<String, dynamic> toJson() {
    return {
      'start_date': startDate,
      'start_time': startTime,
      'finish_date': finishDate,
      'finish_time': finishTime,
    };
  }

  @override
  List<Object?> get props => [startDate, startTime, finishDate, finishTime];
}
