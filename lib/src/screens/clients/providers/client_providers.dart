import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../core/shared/providers/network_api_service_provider.dart';
import '../controllers/client_controller.dart';
import '../repositories/client_repository.dart';

// * Client Repo Provider ========================================
final clientRepoProvider = Provider<ClientRepository>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return ClientRepository(networkApiService: networkApiService);
});

// * Client Controller Provider ========================================
final clientControllerProvider = Provider<ClientController>(
  (ref) {
    final clientRepo = ref.watch(clientRepoProvider);

    return ClientController(
      clientRepo: clientRepo,
    );
  },
);

// * Get Client List Future Provider ========================================
final getClientListFutureProvider = FutureProvider((ref) {
  final clientRepo = ref.watch(clientRepoProvider);

  return clientRepo.getClientList();
});
