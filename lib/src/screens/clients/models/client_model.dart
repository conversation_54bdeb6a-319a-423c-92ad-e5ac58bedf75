import 'package:equatable/equatable.dart';

class ClientListModel extends Equatable {
  final ClientPermissions can;
  final List<Client> clients;

  const ClientListModel({
    this.can = const ClientPermissions(),
    this.clients = const [],
  });

  factory ClientListModel.fromJson(Map<String, dynamic> json) {
    return ClientListModel(
      can: ClientPermissions.fromJson(json['can'] ?? {}),
      clients: (json['dt'] as List<dynamic>?)
              ?.map((e) => Client.fromJson(e))
              .toList() ??
          [],
    );
  }

  factory ClientListModel.empty() => const ClientListModel();

  @override
  List<Object?> get props => [can, clients];
}

class ClientPermissions extends Equatable {
  final bool add;
  final bool edit;
  final bool suspend;

  const ClientPermissions({
    this.add = false,
    this.edit = false,
    this.suspend = false,
  });

  factory ClientPermissions.fromJson(Map<String, dynamic> json) {
    return ClientPermissions(
      add: json['add'] ?? false,
      edit: json['edit'] ?? false,
      suspend: json['suspend'] ?? false,
    );
  }

  @override
  List<Object?> get props => [add, edit, suspend];
}

class Client extends Equatable {
  final int clientID;
  final String clientname;
  final String clientstatus;

  const Client({
    this.clientID = 0,
    this.clientname = '',
    this.clientstatus = '',
  });

  factory Client.fromJson(Map<String, dynamic> json) {
    return Client(
      clientID: json['clientID'] ?? 0,
      clientname: json['clientname'] ?? '',
      clientstatus: json['clientstatus'] ?? '',
    );
  }

  bool get isActive => clientstatus == 'active';
  bool get isLead => clientstatus == 'leads';

  @override
  List<Object?> get props => [clientID, clientname, clientstatus];
}

class AddClientModel extends Equatable {
  final String clientname;
  final String clientstatus;
  final String responsibleName;
  final String responsiblePhone;
  final String responsibleJob;
  final String responsibleEmail;
  final ClientTask task;

  const AddClientModel({
    this.clientname = '',
    this.clientstatus = 'leads',
    this.responsibleName = '',
    this.responsiblePhone = '',
    this.responsibleJob = '',
    this.responsibleEmail = '',
    this.task = const ClientTask(),
  });

  Map<String, dynamic> toJson() {
    return {
      'clientname': clientname,
      'clientstatus': clientstatus,
      'responsible_name': responsibleName,
      'responsible_phone': responsiblePhone,
      'responsible_job': responsibleJob,
      'responsible_email': responsibleEmail,
      'task': task.toJson(),
    };
  }

  @override
  List<Object?> get props => [
        clientname,
        clientstatus,
        responsibleName,
        responsiblePhone,
        responsibleJob,
        responsibleEmail,
        task,
      ];
}

class ClientTask extends Equatable {
  final String startDate;
  final String startTime;
  final String finishDate;
  final String finishTime;

  const ClientTask({
    this.startDate = '',
    this.startTime = '',
    this.finishDate = '',
    this.finishTime = '',
  });

  Map<String, dynamic> toJson() {
    return {
      'start_date': startDate,
      'start_time': startTime,
      'finish_date': finishDate,
      'finish_time': finishTime,
    };
  }

  @override
  List<Object?> get props => [startDate, startTime, finishDate, finishTime];
}
