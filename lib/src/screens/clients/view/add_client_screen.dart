import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/clients/models/client_model.dart';
import 'package:opti_tickets/src/screens/clients/providers/client_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class AddClientScreen extends ConsumerStatefulWidget {
  const AddClientScreen({super.key});

  @override
  ConsumerState<AddClientScreen> createState() => _AddClientScreenState();
}

class _AddClientScreenState extends ConsumerState<AddClientScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _companyNameController;
  late TextEditingController _responsibleNameController;
  late TextEditingController _responsiblePhoneController;
  late TextEditingController _responsibleJobController;
  late TextEditingController _responsibleEmailController;

  String _selectedStatus = 'leads';
  late DateTime _startTime;
  late String _startDate;
  late String _startTimeString;

  @override
  void initState() {
    super.initState();
    _companyNameController = TextEditingController();
    _responsibleNameController = TextEditingController();
    _responsiblePhoneController = TextEditingController();
    _responsibleJobController = TextEditingController();
    _responsibleEmailController = TextEditingController();

    // Record the start time when screen opens
    _startTime = DateTime.now();
    _startDate = DateFormat('yyyy-MM-dd').format(_startTime);
    _startTimeString = DateFormat('HH:mm:ss').format(_startTime);
  }

  @override
  void dispose() {
    _companyNameController.dispose();
    _responsibleNameController.dispose();
    _responsiblePhoneController.dispose();
    _responsibleJobController.dispose();
    _responsibleEmailController.dispose();
    super.dispose();
  }

  Future<void> _submitClient() async {
    if (!_formKey.currentState!.validate()) return;

    // Record finish time
    final finishTime = DateTime.now();
    final finishDate = DateFormat('yyyy-MM-dd').format(finishTime);
    final finishTimeString = DateFormat('HH:mm:ss').format(finishTime);

    final clientData = AddClientModel(
      clientname: _companyNameController.text,
      clientstatus: _selectedStatus,
      responsibleName: _responsibleNameController.text,
      responsiblePhone: _responsiblePhoneController.text,
      responsibleJob: _responsibleJobController.text,
      responsibleEmail: _responsibleEmailController.text,
      task: ClientTask(
        startDate: _startDate,
        startTime: _startTimeString,
        finishDate: finishDate,
        finishTime: finishTimeString,
      ),
    );

    final clientController = ref.read(clientControllerProvider);

    final success = await clientController.addClient(clientData: clientData);

    if (success && mounted) {
      Navigator.pop(context);
      context.showBarMessage(context.tr.clientAdded);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: Text(
          context.tr.addClient,
          style: AppTextStyles.title,
        ),
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_new),
        ),
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Time tracking info
                Container(
                  padding: const EdgeInsets.all(AppSpaces.padding12),
                  decoration: BoxDecoration(
                    color: ColorManager.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppRadius.radius8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        color: ColorManager.primaryColor,
                        size: 20,
                      ),
                      AppGaps.gap8,
                      Text(
                        '${context.tr.currentTime}: $_startTimeString',
                        style: AppTextStyles.labelMedium.copyWith(
                          color: ColorManager.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                AppGaps.gap24,

                // Company Name
                TextFormField(
                  controller: _companyNameController,
                  decoration: InputDecoration(
                    labelText: context.tr.companyName,
                    prefixIcon: const Icon(Icons.business),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.tr.companyNameCannotBeEmpty;
                    }
                    return null;
                  },
                ),
                AppGaps.gap16,

                // Client Status
                DropdownButtonFormField<String>(
                  value: _selectedStatus,
                  decoration: InputDecoration(
                    labelText: context.tr.clientStatus,
                    prefixIcon: const Icon(Icons.flag),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                    ),
                  ),
                  items: [
                    DropdownMenuItem(
                      value: 'leads',
                      child: Text(context.tr.leads),
                    ),
                    DropdownMenuItem(
                      value: 'active',
                      child: Text(context.tr.active),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _selectedStatus = value;
                      });
                    }
                  },
                ),
                AppGaps.gap16,

                // Responsible Name
                TextFormField(
                  controller: _responsibleNameController,
                  decoration: InputDecoration(
                    labelText: context.tr.responsibleName,
                    prefixIcon: const Icon(Icons.person),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.tr.responsibleNameCannotBeEmpty;
                    }
                    return null;
                  },
                ),
                AppGaps.gap16,

                // Responsible Phone
                TextFormField(
                  controller: _responsiblePhoneController,
                  decoration: InputDecoration(
                    labelText: context.tr.responsiblePhone,
                    prefixIcon: const Icon(Icons.phone),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                    ),
                  ),
                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return context.tr.responsiblePhoneCannotBeEmpty;
                    }
                    return null;
                  },
                ),
                AppGaps.gap16,

                // Responsible Job
                TextFormField(
                  controller: _responsibleJobController,
                  decoration: InputDecoration(
                    labelText: context.tr.responsibleJob,
                    prefixIcon: const Icon(Icons.work),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                    ),
                  ),
                ),
                AppGaps.gap16,

                // Responsible Email
                TextFormField(
                  controller: _responsibleEmailController,
                  decoration: InputDecoration(
                    labelText: context.tr.responsibleEmail,
                    prefixIcon: const Icon(Icons.email),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppRadius.radius12),
                    ),
                  ),
                  keyboardType: TextInputType.emailAddress,
                ),
                AppGaps.gap48,

                // Submit Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _submitClient,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: ColorManager.primaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(AppRadius.radius12),
                      ),
                    ),
                    child: Text(
                      context.tr.submit,
                      style: AppTextStyles.subTitle.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
