import 'dart:developer';

import 'package:opti_tickets/src/core/consts/network/api_endpoints.dart';
import 'package:opti_tickets/src/screens/leave/models/leave_model.dart';
import 'package:xr_helper/xr_helper.dart';

class LeaveRepository with BaseRepository {
  final BaseApiServices networkApiService;

  LeaveRepository({
    required this.networkApiService,
  });

  // * Get Leave List
  Future<LeaveListModel> getLeaveList() async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.leaveList;

        final response = await networkApiService.getResponse(url);

        log('afassafas ${response}');

        if (response == null) {
          return LeaveListModel.empty();
        }

        final leaveListModel = LeaveListModel.fromJson(response);

        return leaveListModel;
      },
    );
  }

  // * Add Leave Request
  Future<bool> addLeaveRequest({
    required int leaveType,
    required String fromDate,
    required String toDate,
    required String leaveReason,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.leaveAdd;

        final body = <String, dynamic>{
          'leave_type': leaveType,
          'fromDate': fromDate,
          'toDate': toDate,
          'leave_reason': leaveReason,
        };

        final response = await networkApiService.postResponse(
          url,
          body: body,
        );

        return response['success'] == true;
      },
    );
  }

  // * Update Leave Request
  Future<bool> updateLeaveRequest({
    required int requestId,
    required int leaveType,
    required String fromDate,
    required String toDate,
    required String leaveReason,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.leaveUpdate;

        final body = <String, dynamic>{
          'request_id': requestId,
          'leave_type': leaveType,
          'fromDate': fromDate,
          'toDate': toDate,
          'leave_reason': leaveReason,
        };

        final response = await networkApiService.postResponse(
          url,
          body: body,
        );

        return response['success'] == true;
      },
    );
  }

  // * Delete Leave Request
  Future<bool> deleteLeaveRequest({
    required int requestId,
  }) async {
    return baseFunction(
      () async {
        const url = ApiEndpoints.leaveDelete;

        final body = <String, dynamic>{
          'request_id': requestId,
        };

        final response = await networkApiService.postResponse(
          url,
          body: body,
        );

        return response['success'] == true;
      },
    );
  }
}
