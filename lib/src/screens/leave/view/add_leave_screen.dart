import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/leave/models/leave_model.dart';
import 'package:opti_tickets/src/screens/leave/providers/leave_providers.dart';
import 'package:xr_helper/xr_helper.dart';

class AddLeaveScreen extends ConsumerStatefulWidget {
  final LeaveRequest? leaveRequest;

  const AddLeaveScreen({
    super.key,
    this.leaveRequest,
  });

  @override
  ConsumerState<AddLeaveScreen> createState() => _AddLeaveScreenState();
}

class _AddLeaveScreenState extends ConsumerState<AddLeaveScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _reasonController;
  late TextEditingController _fromDateController;
  late TextEditingController _toDateController;

  LeaveType? _selectedLeaveType;
  DateTime? _fromDate;
  DateTime? _toDate;

  bool get isEditing => widget.leaveRequest != null;

  @override
  void initState() {
    super.initState();
    _reasonController = TextEditingController();
    _fromDateController = TextEditingController();
    _toDateController = TextEditingController();

    if (isEditing) {
      _reasonController.text = widget.leaveRequest!.ulrReasonEmp;
      _fromDate = DateTime.tryParse(widget.leaveRequest!.ulrFromDate);
      _toDate = DateTime.tryParse(widget.leaveRequest!.ulrToDate);

      if (_fromDate != null) {
        _fromDateController.text = DateFormat('yyyy-MM-dd').format(_fromDate!);
      }
      if (_toDate != null) {
        _toDateController.text = DateFormat('yyyy-MM-dd').format(_toDate!);
      }

      // Find matching leave type
      _selectedLeaveType = leaveTypes.firstWhere(
        (type) =>
            type.nameEn == widget.leaveRequest!.ulrLeaveType ||
            type.nameAr == widget.leaveRequest!.ulrLeaveType,
        orElse: () => leaveTypes.first,
      );
    }
  }

  @override
  void dispose() {
    _reasonController.dispose();
    _fromDateController.dispose();
    _toDateController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          isFromDate ? _fromDate ?? DateTime.now() : _toDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          _fromDate = picked;
          _fromDateController.text =
              DateFormat('yyyy-MM-dd', 'en').format(picked);
          // Reset to date if it's before from date
          if (_toDate != null && _toDate!.isBefore(picked)) {
            _toDate = null;
            _toDateController.clear();
          }
        } else {
          _toDate = picked;
          _toDateController.text =
              DateFormat('yyyy-MM-dd', 'en').format(picked);
        }
      });
    }
  }

  Future<void> _submitLeave() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedLeaveType == null) {
      context.showBarMessage(context.tr.selectLeaveType);
      return;
    }

    final leaveController = ref.read(leaveControllerProvider);

    bool success;
    if (isEditing) {
      success = await leaveController.updateLeaveRequest(
        requestId: widget.leaveRequest!.ulrId,
        leaveType: _selectedLeaveType!.id,
        fromDate: _fromDateController.text,
        toDate: _toDateController.text,
        leaveReason: _reasonController.text,
      );
    } else {
      success = await leaveController.addLeaveRequest(
        leaveType: _selectedLeaveType!.id,
        fromDate: _fromDateController.text,
        toDate: _toDateController.text,
        leaveReason: _reasonController.text,
      );
    }

    if (success && mounted) {
      Navigator.pop(context);
      context.showBarMessage(
        isEditing
            ? context.tr.leaveRequestUpdated
            : context.tr.leaveRequestAdded,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: Text(
          isEditing ? context.tr.editLeaveRequest : context.tr.addLeaveRequest,
          style: AppTextStyles.title,
        ),
        centerTitle: true,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: const Icon(Icons.arrow_back_ios_new),
        ),
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(AppSpaces.padding16),
          child: Column(
            children: [
              // Leave Type Dropdown
              DropdownButtonFormField<LeaveType>(
                value: _selectedLeaveType,
                decoration: InputDecoration(
                  labelText: context.tr.leaveType,
                  prefixIcon: const Icon(Icons.category),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                ),
                items: leaveTypes.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(
                      !context.isEnglish ? type.nameAr : type.nameEn,
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedLeaveType = value;
                  });
                },
                validator: (value) {
                  if (value == null) {
                    return context.tr.selectLeaveType;
                  }
                  return null;
                },
              ),
              AppGaps.gap16,

              // From Date
              TextFormField(
                controller: _fromDateController,
                decoration: InputDecoration(
                  labelText: context.tr.fromDate,
                  prefixIcon: const Icon(Icons.calendar_today),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                ),
                readOnly: true,
                onTap: () => _selectDate(context, true),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return context.tr.fromDateCannotBeEmpty;
                  }
                  return null;
                },
              ),
              AppGaps.gap16,

              // To Date
              TextFormField(
                controller: _toDateController,
                decoration: InputDecoration(
                  labelText: context.tr.toDate,
                  prefixIcon: const Icon(Icons.calendar_today),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                ),
                readOnly: true,
                onTap: () => _selectDate(context, false),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return context.tr.toDateCannotBeEmpty;
                  }
                  return null;
                },
              ),
              AppGaps.gap16,

              // Reason
              TextFormField(
                controller: _reasonController,
                decoration: InputDecoration(
                  labelText: context.tr.reason,
                  prefixIcon: const Icon(Icons.description),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.radius12),
                  ),
                ),
                maxLines: 3,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return context.tr.reasonCannotBeEmpty;
                  }
                  return null;
                },
              ),
              AppGaps.gap32,

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: Button(
                  isLoading:
                      ref.watch(leaveControllerNotifierProvider).isLoading,
                  onPressed: _submitLeave,
                  label: context.tr.submit,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
