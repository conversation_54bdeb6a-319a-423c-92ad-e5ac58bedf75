import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/leave/models/leave_model.dart';
import 'package:opti_tickets/src/screens/leave/providers/leave_providers.dart';
import 'package:opti_tickets/src/screens/leave/view/add_leave_screen.dart';
import 'package:opti_tickets/src/screens/leave/view/widgets/leave_card_widget.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:xr_helper/xr_helper.dart';

class LeaveListScreen extends ConsumerWidget {
  const LeaveListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final leaveFuture = ref.watch(getLeaveListFutureProvider);

    final leaveModel = leaveFuture.when(
      data: (leaveData) => leaveData,
      loading: () => const LeaveListModel(),
      error: (error, stackTrace) => const LeaveListModel(),
    );

    final isLoading = leaveFuture.isLoading;

    return Scaffold(
      backgroundColor: ColorManager.lightGreyBackground,
      appBar: AppBar(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.transparent,
        title: Text(
          context.tr.leaveRequests,
          style: AppTextStyles.title,
        ),
        centerTitle: true,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const AddLeaveScreen(),
                ),
              ).then((_) {
                // Refresh the list when returning from add screen
                ref.invalidate(getLeaveListFutureProvider);
              });
            },
            icon: const Icon(Icons.add),
          ),
        ],
      ),
      body: Skeletonizer(
        enabled: isLoading,
        child: leaveModel.leaves.isEmpty && !isLoading
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 64,
                      color: ColorManager.lightGrey,
                    ),
                    AppGaps.gap16,
                    Text(
                      context.tr.noDataFound,
                      style: AppTextStyles.subTitle.copyWith(
                        color: ColorManager.lightGrey,
                      ),
                    ),
                  ],
                ),
              )
            : RefreshIndicator(
                onRefresh: () async {
                  ref.invalidate(getLeaveListFutureProvider);
                },
                child: ListView.builder(
                  padding: const EdgeInsets.all(AppSpaces.padding16),
                  itemCount: isLoading ? 5 : leaveModel.leaves.length,
                  itemBuilder: (context, index) {
                    if (isLoading) {
                      return const Padding(
                        padding: EdgeInsets.only(bottom: AppSpaces.padding16),
                        child: LeaveCardWidget(
                          leave: LeaveRequest(),
                        ),
                      );
                    }

                    final leave = leaveModel.leaves[index];
                    return Padding(
                      padding:
                          const EdgeInsets.only(bottom: AppSpaces.padding16),
                      child: LeaveCardWidget(
                        leave: leave,
                        canTakeAction: leaveModel.canTakeAction,
                        onEdit: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => AddLeaveScreen(
                                leaveRequest: leave,
                              ),
                            ),
                          ).then((_) {
                            ref.invalidate(getLeaveListFutureProvider);
                          });
                        },
                        onDelete: () async {
                          final confirmed = await showDialog<bool>(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: Text(context.tr.deleteLeaveRequest),
                              content: Text(context.tr.areYouSureDeleteLeave),
                              actions: [
                                TextButton(
                                  onPressed: () =>
                                      Navigator.pop(context, false),
                                  child: Text(context.tr.cancel),
                                ),
                                TextButton(
                                  onPressed: () => Navigator.pop(context, true),
                                  child: Text(context.tr.confirm),
                                ),
                              ],
                            ),
                          );

                          if (confirmed == true) {
                            final leaveController =
                                ref.read(leaveControllerProvider);
                            final success =
                                await leaveController.deleteLeaveRequest(
                              requestId: leave.ulrId,
                            );

                            if (success && context.mounted) {
                              context.showBarMessage(
                                  context.tr.leaveRequestDeleted);
                              ref.invalidate(getLeaveListFutureProvider);
                            }
                          }
                        },
                      ),
                    );
                  },
                ),
              ),
      ),
    );
  }
}
