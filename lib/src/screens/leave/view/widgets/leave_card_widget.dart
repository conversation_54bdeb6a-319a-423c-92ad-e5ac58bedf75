import 'package:flutter/material.dart';
import 'package:opti_tickets/src/core/shared/extensions/context_extensions.dart';
import 'package:opti_tickets/src/core/theme/color_manager.dart';
import 'package:opti_tickets/src/screens/leave/models/leave_model.dart';
import 'package:xr_helper/xr_helper.dart';

class LeaveCardWidget extends StatelessWidget {
  final LeaveRequest leave;
  final bool canTakeAction;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const LeaveCardWidget({
    super.key,
    required this.leave,
    this.canTakeAction = false,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final statusColor = leave.ulrStatus.statusColor.isNotEmpty
        ? Color(int.parse(leave.ulrStatus.statusColor.replaceFirst('#', '0xFF')))
        : ColorManager.lightGrey;

    return Container(
      padding: const EdgeInsets.all(AppSpaces.padding16),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppRadius.radius12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with status and actions
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppSpaces.padding8,
                    vertical: AppSpaces.padding4,
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppRadius.radius8),
                  ),
                  child: Text(
                    !context.isEnglish
                        ? leave.ulrStatus.statusNameA
                        : leave.ulrStatus.statusNameE,
                    style: AppTextStyles.caption.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
              if (canTakeAction && leave.ulrStatus.status == 'pending') ...[
                AppGaps.gap8,
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'edit') {
                      onEdit?.call();
                    } else if (value == 'delete') {
                      onDelete?.call();
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          const Icon(Icons.edit, size: 16),
                          AppGaps.gap8,
                          Text(context.tr.editLeaveRequest),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          const Icon(Icons.delete, size: 16, color: Colors.red),
                          AppGaps.gap8,
                          Text(
                            context.tr.deleteLeaveRequest,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
          AppGaps.gap12,

          // Leave Type
          Text(
            leave.ulrLeaveType,
            style: AppTextStyles.subTitle.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          AppGaps.gap8,

          // Date Range
          Row(
            children: [
              const Icon(
                Icons.calendar_today,
                size: 16,
                color: ColorManager.lightGrey,
              ),
              AppGaps.gap8,
              Text(
                '${leave.ulrFromDate} - ${leave.ulrToDate}',
                style: AppTextStyles.caption.copyWith(
                  color: ColorManager.lightGrey,
                ),
              ),
            ],
          ),
          AppGaps.gap8,

          // Reason
          if (leave.ulrReasonEmp.isNotEmpty) ...[
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Icon(
                  Icons.description,
                  size: 16,
                  color: ColorManager.lightGrey,
                ),
                AppGaps.gap8,
                Expanded(
                  child: Text(
                    leave.ulrReasonEmp,
                    style: AppTextStyles.caption.copyWith(
                      color: ColorManager.lightGrey,
                    ),
                  ),
                ),
              ],
            ),
            AppGaps.gap8,
          ],

          // Issued Date
          Row(
            children: [
              const Icon(
                Icons.access_time,
                size: 16,
                color: ColorManager.lightGrey,
              ),
              AppGaps.gap8,
              Text(
                '${context.tr.issuedAt}: ${leave.ulrIssuedAt}',
                style: AppTextStyles.caption.copyWith(
                  color: ColorManager.lightGrey,
                ),
              ),
            ],
          ),

          // Status message if available
          if (leave.ulrStatus.message != null && leave.ulrStatus.message!.isNotEmpty) ...[
            AppGaps.gap8,
            Container(
              padding: const EdgeInsets.all(AppSpaces.padding8),
              decoration: BoxDecoration(
                color: ColorManager.lightGreyBackground,
                borderRadius: BorderRadius.circular(AppRadius.radius8),
              ),
              child: Text(
                leave.ulrStatus.message!,
                style: AppTextStyles.caption,
              ),
            ),
          ],
        ],
      ),
    );
  }
}
