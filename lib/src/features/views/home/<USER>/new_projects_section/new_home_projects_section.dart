import 'package:flutter/material.dart';
import 'package:page/src/core/config/constants.dart';
import 'package:page/src/core/localization/app_localizations.dart';
import 'package:page/src/core/services/api.dart';
import 'package:page/src/features/models/static_project_types.dart';
import 'package:page/src/features/models/video_model.dart';
import 'package:page/src/features/views/projects/filtered_projects_page.dart';

import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../models/content.dart';
import '../../../story/widgets/reel_widgets/reel_details.dart';
import 'horizontal_project_card.dart';

class NewHomeProjectsSection extends StatefulWidget {
  const NewHomeProjectsSection({super.key});

  @override
  State<NewHomeProjectsSection> createState() => _NewHomeProjectsSectionState();
}

class _NewHomeProjectsSectionState extends State<NewHomeProjectsSection> {
  List<VideoModel> apartmentProjects = [];
  List<VideoModel> villaProjects = [];
  List<VideoModel> luxuryProjects = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    currencyController.getcuurentcurrency(context).then((value) {
      setState(() {});
    });

    _loadHomeData();
  }

  Future<void> _loadHomeData() async {
    try {
      final homeResponse = await Api.gethome();
      if (homeResponse.code == 1) {
        setState(() {
          apartmentProjects = homeResponse.type1;
          villaProjects = homeResponse.type2;
          luxuryProjects = homeResponse.type3;
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  bool isEnglish(BuildContext context) {
    return Localizations.localeOf(context).languageCode == 'en';
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Center(
        child: buildLoadingWidget(),
      );
    }

    return Column(
      children: [
        // Apartments Section
        _buildProjectSection(
          title: AppLocalizations.of(context)
              .translate('New Projects - Apartments'),
          projects: apartmentProjects,
          type: StaticProjectTypes.apartmentsType,
        ),

        const SizedBox(height: 20),

        // Villa Section
        _buildProjectSection(
          title: AppLocalizations.of(context).translate('New Projects - Villa'),
          projects: villaProjects,
          type: StaticProjectTypes.villaType,
        ),

        const SizedBox(height: 20),

        // Luxury Section
        _buildProjectSection(
          title:
              AppLocalizations.of(context).translate('New Projects - Luxury'),
          projects: luxuryProjects,
          type: StaticProjectTypes.luxuryType,
        ),
      ],
    );
  }

  Widget _buildProjectSection({
    required String title,
    required List<VideoModel> projects,
    required Types type,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => FilteredProjectsPage(type: type),
                    ),
                  );
                },
                child: Text(
                  AppLocalizations.of(context).translate('See More'),
                  style: TextStyle(
                    fontSize: 14,
                    color: primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Horizontal scrolling projects
        projects.isNotEmpty
            ? SizedBox(
                height: 220,
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  scrollDirection: Axis.horizontal,
                  itemCount: projects.length,
                  itemBuilder: (context, index) {
                    return Container(
                      width: 300,
                      margin: EdgeInsets.only(
                        right: index < projects.length - 1 ? 16 : 0,
                      ),
                      child: HorizontalProjectCard(
                        project: projects[index],
                      ),
                    );
                  },
                ),
              )
            : Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: nodatafound(
                  AppLocalizations.of(context).translate('No Projects to show'),
                ),
              ),
      ],
    );
  }
}
