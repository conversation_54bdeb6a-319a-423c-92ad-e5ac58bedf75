{"noDataFound": "No data found", "enter": "Enter", "pickImage": "Pick Image", "search": "Search", "welcomeWithName": "Welcome, {name}", "home": "Home", "register": "Register", "login": "<PERSON><PERSON>", "remainingMaintenance": "Remaining Maintenance", "mySubscriptions": "My Subscriptions", "days": "Days", "issuerName": "Issuer Name", "issuerEmail": "Issuer <PERSON><PERSON>", "issuerPhone": "Issuer Phone", "attachment": "Attachment", "description": "Description", "submit": "Submit", "active": "Active", "archived": "Archived", "recentActiveTickets": "Recent Active Tickets", "noRepliesFound": "No replies found", "replies": "Replies", "totalTickets": "Total Tickets", "startDate": "Start Date", "endDate": "End Date", "areYouSureYouWantToLogout": "Are you sure you want to logout?", "status": "Status", "reply": "Reply", "gallery": "Gallery", "camera": "Camera", "replyCannotBeEmpty": "Reply cannot be empty", "logout": "Logout", "replySentSuccessfully": "Reply sent successfully", "english": "English", "arabic": "العربية", "cancel": "Cancel", "changeLanguage": "Change Language", "password": "Password", "save": "Save", "welcomeBack": "Welcome back", "welcomeBackLine": "Welcome\nback", "reports": "Reports", "itsGreatToSeeYou": "It's great to see you", "allTickets": "All Tickets", "username": "Username", "sortByDate": "Sort by Date", "repliedOnTheTicket": "Replied on the ticket", "newReplyOnTicket": "New reply on ticket", "ascending": "Ascending", "confirm": "Confirm", "descending": "Descending", "request": "Request", "issue": "Issue", "addNewTicket": "Add New Ticket", "currentTime": "Current Time", "attendanceTime": "Attendance Time", "location": "Location", "monthlyStats": "Monthly Statistics", "completeAttends": "Complete Attends", "incompleteAttends": "Incomplete Attends", "absents": "Absents", "officialHolidays": "Official Holidays", "vacationLeaves": "Vacation Leaves", "requestLeaves": "Request Leaves", "sickLeaves": "Sick Leaves", "activeTasks": "Active Tasks", "finishedTasks": "Finished Tasks", "checkIn": "Check In", "checkOut": "Check Out", "workTime": "Work Time", "contractsExpiring": "Contracts Almost Expired", "contractCode": "Contract Code", "clientName": "Client Name", "somethingWentWrong": "Something went wrong", "expiryDate": "Expiry Date", "remainingDays": "Remaining Days", "biometricNotAvailable": "Biometric authentication is not available on this device", "authenticateToCheckIn": "Please authenticate to check in", "authenticateToCheckOut": "Please authenticate to check out", "authenticationFailed": "Authentication failed", "checkInSuccessful": "Check-in successful", "checkOutSuccessful": "Check-out successful", "checkInFailed": "Check-in failed", "checkOutFailed": "Check-out failed", "summary": "Summary", "settings": "Settings", "theme": "Theme", "language": "Language", "systemSetting": "System Setting", "light": "Light", "dark": "Dark", "profile": "Profile", "editProfile": "Edit Profile", "mobile": "Mobile", "email": "Email", "profilePicture": "Profile Picture", "updateProfile": "Update Profile", "profileUpdatedSuccessfully": "Profile updated successfully", "leaves": "Leaves", "leaveRequests": "Leave Requests", "addLeaveRequest": "Add Leave Request", "editLeaveRequest": "Edit Leave Request", "leaveType": "Leave Type", "fromDate": "From Date", "toDate": "To Date", "reason": "Reason", "vacationLeave": "Vacation Leave", "sickLeave": "Sick Leave", "requestLeave": "Request Leave", "leaveRequestAdded": "Leave request added successfully", "leaveRequestUpdated": "Leave request updated successfully", "leaveRequestDeleted": "Leave request deleted successfully", "deleteLeaveRequest": "Delete Leave Request", "areYouSureDeleteLeave": "Are you sure you want to delete this leave request?", "clients": "Clients", "addClient": "Add Client", "companyName": "Company Name", "responsibleName": "Responsible Name", "responsiblePhone": "Responsible Phone", "responsibleJob": "Responsible Job", "responsibleEmail": "Responsible Email", "clientStatus": "Client Status", "leads": "Leads", "clientAdded": "Client added successfully", "approved": "Approved", "pending": "Pending", "rejected": "Rejected", "issuedAt": "Issued At", "selectDate": "Select Date", "selectLeaveType": "Select Leave Type", "reasonCannotBeEmpty": "Reason cannot be empty", "fromDateCannotBeEmpty": "From date cannot be empty", "toDateCannotBeEmpty": "To date cannot be empty", "companyNameCannotBeEmpty": "Company name cannot be empty", "responsibleNameCannotBeEmpty": "Responsible name cannot be empty", "responsiblePhoneCannotBeEmpty": "Responsible phone cannot be empty", "tickets": "Tickets", "contracts": "Contracts", "nearExpire": "Near Expire", "expired": "Expired", "agreements": "Agreements", "quotations": "Quotations", "quotationsList": "Quotations List", "saveQuotation": "Save Quotation", "editQuotation": "Edit Quotation", "licenses": "Licenses", "subscriptions": "Subscriptions", "maintenance": "Maintenance", "helpCenter": "Help Center", "myTickets": "My Tickets", "archivedTickets": "Archived Tickets", "allArchivedTickets": "All Archived Tickets", "softwareManagement": "Software Management", "clientsList": "Clients List", "meetings": "Meetings", "clientOpinions": "Client Opinions", "companyRegistration": "Company Registration", "clientID": "Client ID", "createdDate": "Created Date", "validityDays": "Validity Days", "productId": "Product ID", "subProductId": "Sub Product ID", "hasLimitUsers": "Has Limit Users", "quotationCode": "Quotation Code", "quotationStatus": "Quotation Status", "serviceName": "Service Name", "subServiceName": "Sub Service Name", "quotationSaved": "Quotation saved successfully", "selectClient": "Select Client", "selectProduct": "Select Product", "selectSubProduct": "Select Sub Product", "retry": "Retry", "selectProductsAndServices": "Select Products & Services", "done": "Done", "noProductsAvailable": "No products available", "failedToLoadProducts": "Failed to load products", "selectSubServices": "Select Sub-Services", "cost": "Cost", "hasUserLimit": "<PERSON> <PERSON>r <PERSON>it", "productsSelected": "products selected", "productSelected": "product selected", "pleaseSelectAtLeastOneProduct": "Please select at least one product"}