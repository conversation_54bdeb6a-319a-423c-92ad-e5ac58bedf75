// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

// **************************************************************************
// Generator: Flutter Intl IDE plugin
// Made by Localizely
// **************************************************************************

// ignore_for_file: non_constant_identifier_names, lines_longer_than_80_chars
// ignore_for_file: join_return_with_assignment, prefer_final_in_for_each
// ignore_for_file: avoid_redundant_argument_values, avoid_escaping_inner_quotes

class S {
  S();

  static S? _current;

  static S get current {
    assert(
      _current != null,
      'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.',
    );
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(
      instance != null,
      'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?',
    );
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of<S>(context, S);
  }

  /// `No data found`
  String get noDataFound {
    return Intl.message(
      'No data found',
      name: 'noDataFound',
      desc: '',
      args: [],
    );
  }

  /// `Enter`
  String get enter {
    return Intl.message('Enter', name: 'enter', desc: '', args: []);
  }

  /// `Pick Image`
  String get pickImage {
    return Intl.message('Pick Image', name: 'pickImage', desc: '', args: []);
  }

  /// `Search`
  String get search {
    return Intl.message('Search', name: 'search', desc: '', args: []);
  }

  /// `Welcome, {name}`
  String welcomeWithName(Object name) {
    return Intl.message(
      'Welcome, $name',
      name: 'welcomeWithName',
      desc: '',
      args: [name],
    );
  }

  /// `Home`
  String get home {
    return Intl.message('Home', name: 'home', desc: '', args: []);
  }

  /// `Register`
  String get register {
    return Intl.message('Register', name: 'register', desc: '', args: []);
  }

  /// `Login`
  String get login {
    return Intl.message('Login', name: 'login', desc: '', args: []);
  }

  /// `Remaining Maintenance`
  String get remainingMaintenance {
    return Intl.message(
      'Remaining Maintenance',
      name: 'remainingMaintenance',
      desc: '',
      args: [],
    );
  }

  /// `My Subscriptions`
  String get mySubscriptions {
    return Intl.message(
      'My Subscriptions',
      name: 'mySubscriptions',
      desc: '',
      args: [],
    );
  }

  /// `Days`
  String get days {
    return Intl.message('Days', name: 'days', desc: '', args: []);
  }

  /// `Issuer Name`
  String get issuerName {
    return Intl.message('Issuer Name', name: 'issuerName', desc: '', args: []);
  }

  /// `Issuer Email`
  String get issuerEmail {
    return Intl.message(
      'Issuer Email',
      name: 'issuerEmail',
      desc: '',
      args: [],
    );
  }

  /// `Issuer Phone`
  String get issuerPhone {
    return Intl.message(
      'Issuer Phone',
      name: 'issuerPhone',
      desc: '',
      args: [],
    );
  }

  /// `Attachment`
  String get attachment {
    return Intl.message('Attachment', name: 'attachment', desc: '', args: []);
  }

  /// `Description`
  String get description {
    return Intl.message('Description', name: 'description', desc: '', args: []);
  }

  /// `Submit`
  String get submit {
    return Intl.message('Submit', name: 'submit', desc: '', args: []);
  }

  /// `Active`
  String get active {
    return Intl.message('Active', name: 'active', desc: '', args: []);
  }

  /// `Archived`
  String get archived {
    return Intl.message('Archived', name: 'archived', desc: '', args: []);
  }

  /// `Recent Active Tickets`
  String get recentActiveTickets {
    return Intl.message(
      'Recent Active Tickets',
      name: 'recentActiveTickets',
      desc: '',
      args: [],
    );
  }

  /// `No replies found`
  String get noRepliesFound {
    return Intl.message(
      'No replies found',
      name: 'noRepliesFound',
      desc: '',
      args: [],
    );
  }

  /// `Replies`
  String get replies {
    return Intl.message('Replies', name: 'replies', desc: '', args: []);
  }

  /// `Total Tickets`
  String get totalTickets {
    return Intl.message(
      'Total Tickets',
      name: 'totalTickets',
      desc: '',
      args: [],
    );
  }

  /// `Start Date`
  String get startDate {
    return Intl.message('Start Date', name: 'startDate', desc: '', args: []);
  }

  /// `End Date`
  String get endDate {
    return Intl.message('End Date', name: 'endDate', desc: '', args: []);
  }

  /// `Are you sure you want to logout?`
  String get areYouSureYouWantToLogout {
    return Intl.message(
      'Are you sure you want to logout?',
      name: 'areYouSureYouWantToLogout',
      desc: '',
      args: [],
    );
  }

  /// `Status`
  String get status {
    return Intl.message('Status', name: 'status', desc: '', args: []);
  }

  /// `Reply`
  String get reply {
    return Intl.message('Reply', name: 'reply', desc: '', args: []);
  }

  /// `Gallery`
  String get gallery {
    return Intl.message('Gallery', name: 'gallery', desc: '', args: []);
  }

  /// `Camera`
  String get camera {
    return Intl.message('Camera', name: 'camera', desc: '', args: []);
  }

  /// `Reply cannot be empty`
  String get replyCannotBeEmpty {
    return Intl.message(
      'Reply cannot be empty',
      name: 'replyCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Logout`
  String get logout {
    return Intl.message('Logout', name: 'logout', desc: '', args: []);
  }

  /// `Reply sent successfully`
  String get replySentSuccessfully {
    return Intl.message(
      'Reply sent successfully',
      name: 'replySentSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `English`
  String get english {
    return Intl.message('English', name: 'english', desc: '', args: []);
  }

  /// `العربية`
  String get arabic {
    return Intl.message('العربية', name: 'arabic', desc: '', args: []);
  }

  /// `Cancel`
  String get cancel {
    return Intl.message('Cancel', name: 'cancel', desc: '', args: []);
  }

  /// `Change Language`
  String get changeLanguage {
    return Intl.message(
      'Change Language',
      name: 'changeLanguage',
      desc: '',
      args: [],
    );
  }

  /// `Password`
  String get password {
    return Intl.message('Password', name: 'password', desc: '', args: []);
  }

  /// `Save`
  String get save {
    return Intl.message('Save', name: 'save', desc: '', args: []);
  }

  /// `Welcome back`
  String get welcomeBack {
    return Intl.message(
      'Welcome back',
      name: 'welcomeBack',
      desc: '',
      args: [],
    );
  }

  /// `Welcome\nback`
  String get welcomeBackLine {
    return Intl.message(
      'Welcome\nback',
      name: 'welcomeBackLine',
      desc: '',
      args: [],
    );
  }

  /// `Reports`
  String get reports {
    return Intl.message('Reports', name: 'reports', desc: '', args: []);
  }

  /// `It's great to see you`
  String get itsGreatToSeeYou {
    return Intl.message(
      'It\'s great to see you',
      name: 'itsGreatToSeeYou',
      desc: '',
      args: [],
    );
  }

  /// `All Tickets`
  String get allTickets {
    return Intl.message('All Tickets', name: 'allTickets', desc: '', args: []);
  }

  /// `Username`
  String get username {
    return Intl.message('Username', name: 'username', desc: '', args: []);
  }

  /// `Sort by Date`
  String get sortByDate {
    return Intl.message('Sort by Date', name: 'sortByDate', desc: '', args: []);
  }

  /// `Replied on the ticket`
  String get repliedOnTheTicket {
    return Intl.message(
      'Replied on the ticket',
      name: 'repliedOnTheTicket',
      desc: '',
      args: [],
    );
  }

  /// `New reply on ticket`
  String get newReplyOnTicket {
    return Intl.message(
      'New reply on ticket',
      name: 'newReplyOnTicket',
      desc: '',
      args: [],
    );
  }

  /// `Ascending`
  String get ascending {
    return Intl.message('Ascending', name: 'ascending', desc: '', args: []);
  }

  /// `Confirm`
  String get confirm {
    return Intl.message('Confirm', name: 'confirm', desc: '', args: []);
  }

  /// `Descending`
  String get descending {
    return Intl.message('Descending', name: 'descending', desc: '', args: []);
  }

  /// `Request`
  String get request {
    return Intl.message('Request', name: 'request', desc: '', args: []);
  }

  /// `Issue`
  String get issue {
    return Intl.message('Issue', name: 'issue', desc: '', args: []);
  }

  /// `Add New Ticket`
  String get addNewTicket {
    return Intl.message(
      'Add New Ticket',
      name: 'addNewTicket',
      desc: '',
      args: [],
    );
  }

  /// `Current Time`
  String get currentTime {
    return Intl.message(
      'Current Time',
      name: 'currentTime',
      desc: '',
      args: [],
    );
  }

  /// `Attendance Time`
  String get attendanceTime {
    return Intl.message(
      'Attendance Time',
      name: 'attendanceTime',
      desc: '',
      args: [],
    );
  }

  /// `Location`
  String get location {
    return Intl.message('Location', name: 'location', desc: '', args: []);
  }

  /// `Monthly Statistics`
  String get monthlyStats {
    return Intl.message(
      'Monthly Statistics',
      name: 'monthlyStats',
      desc: '',
      args: [],
    );
  }

  /// `Complete Attends`
  String get completeAttends {
    return Intl.message(
      'Complete Attends',
      name: 'completeAttends',
      desc: '',
      args: [],
    );
  }

  /// `Incomplete Attends`
  String get incompleteAttends {
    return Intl.message(
      'Incomplete Attends',
      name: 'incompleteAttends',
      desc: '',
      args: [],
    );
  }

  /// `Absents`
  String get absents {
    return Intl.message('Absents', name: 'absents', desc: '', args: []);
  }

  /// `Official Holidays`
  String get officialHolidays {
    return Intl.message(
      'Official Holidays',
      name: 'officialHolidays',
      desc: '',
      args: [],
    );
  }

  /// `Vacation Leaves`
  String get vacationLeaves {
    return Intl.message(
      'Vacation Leaves',
      name: 'vacationLeaves',
      desc: '',
      args: [],
    );
  }

  /// `Request Leaves`
  String get requestLeaves {
    return Intl.message(
      'Request Leaves',
      name: 'requestLeaves',
      desc: '',
      args: [],
    );
  }

  /// `Sick Leaves`
  String get sickLeaves {
    return Intl.message('Sick Leaves', name: 'sickLeaves', desc: '', args: []);
  }

  /// `Active Tasks`
  String get activeTasks {
    return Intl.message(
      'Active Tasks',
      name: 'activeTasks',
      desc: '',
      args: [],
    );
  }

  /// `Finished Tasks`
  String get finishedTasks {
    return Intl.message(
      'Finished Tasks',
      name: 'finishedTasks',
      desc: '',
      args: [],
    );
  }

  /// `Check In`
  String get checkIn {
    return Intl.message('Check In', name: 'checkIn', desc: '', args: []);
  }

  /// `Check Out`
  String get checkOut {
    return Intl.message('Check Out', name: 'checkOut', desc: '', args: []);
  }

  /// `Work Time`
  String get workTime {
    return Intl.message('Work Time', name: 'workTime', desc: '', args: []);
  }

  /// `Contracts Almost Expired`
  String get contractsExpiring {
    return Intl.message(
      'Contracts Almost Expired',
      name: 'contractsExpiring',
      desc: '',
      args: [],
    );
  }

  /// `Contract Code`
  String get contractCode {
    return Intl.message(
      'Contract Code',
      name: 'contractCode',
      desc: '',
      args: [],
    );
  }

  /// `Client Name`
  String get clientName {
    return Intl.message('Client Name', name: 'clientName', desc: '', args: []);
  }

  /// `Something went wrong`
  String get somethingWentWrong {
    return Intl.message(
      'Something went wrong',
      name: 'somethingWentWrong',
      desc: '',
      args: [],
    );
  }

  /// `Expiry Date`
  String get expiryDate {
    return Intl.message('Expiry Date', name: 'expiryDate', desc: '', args: []);
  }

  /// `Remaining Days`
  String get remainingDays {
    return Intl.message(
      'Remaining Days',
      name: 'remainingDays',
      desc: '',
      args: [],
    );
  }

  /// `Biometric authentication is not available on this device`
  String get biometricNotAvailable {
    return Intl.message(
      'Biometric authentication is not available on this device',
      name: 'biometricNotAvailable',
      desc: '',
      args: [],
    );
  }

  /// `Please authenticate to check in`
  String get authenticateToCheckIn {
    return Intl.message(
      'Please authenticate to check in',
      name: 'authenticateToCheckIn',
      desc: '',
      args: [],
    );
  }

  /// `Please authenticate to check out`
  String get authenticateToCheckOut {
    return Intl.message(
      'Please authenticate to check out',
      name: 'authenticateToCheckOut',
      desc: '',
      args: [],
    );
  }

  /// `Authentication failed`
  String get authenticationFailed {
    return Intl.message(
      'Authentication failed',
      name: 'authenticationFailed',
      desc: '',
      args: [],
    );
  }

  /// `Check-in successful`
  String get checkInSuccessful {
    return Intl.message(
      'Check-in successful',
      name: 'checkInSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Check-out successful`
  String get checkOutSuccessful {
    return Intl.message(
      'Check-out successful',
      name: 'checkOutSuccessful',
      desc: '',
      args: [],
    );
  }

  /// `Check-in failed`
  String get checkInFailed {
    return Intl.message(
      'Check-in failed',
      name: 'checkInFailed',
      desc: '',
      args: [],
    );
  }

  /// `Check-out failed`
  String get checkOutFailed {
    return Intl.message(
      'Check-out failed',
      name: 'checkOutFailed',
      desc: '',
      args: [],
    );
  }

  /// `Summary`
  String get summary {
    return Intl.message('Summary', name: 'summary', desc: '', args: []);
  }

  /// `Settings`
  String get settings {
    return Intl.message('Settings', name: 'settings', desc: '', args: []);
  }

  /// `Theme`
  String get theme {
    return Intl.message('Theme', name: 'theme', desc: '', args: []);
  }

  /// `Language`
  String get language {
    return Intl.message('Language', name: 'language', desc: '', args: []);
  }

  /// `System Setting`
  String get systemSetting {
    return Intl.message(
      'System Setting',
      name: 'systemSetting',
      desc: '',
      args: [],
    );
  }

  /// `Light`
  String get light {
    return Intl.message('Light', name: 'light', desc: '', args: []);
  }

  /// `Dark`
  String get dark {
    return Intl.message('Dark', name: 'dark', desc: '', args: []);
  }

  /// `Profile`
  String get profile {
    return Intl.message('Profile', name: 'profile', desc: '', args: []);
  }

  /// `Edit Profile`
  String get editProfile {
    return Intl.message(
      'Edit Profile',
      name: 'editProfile',
      desc: '',
      args: [],
    );
  }

  /// `Mobile`
  String get mobile {
    return Intl.message('Mobile', name: 'mobile', desc: '', args: []);
  }

  /// `Email`
  String get email {
    return Intl.message('Email', name: 'email', desc: '', args: []);
  }

  /// `Profile Picture`
  String get profilePicture {
    return Intl.message(
      'Profile Picture',
      name: 'profilePicture',
      desc: '',
      args: [],
    );
  }

  /// `Update Profile`
  String get updateProfile {
    return Intl.message(
      'Update Profile',
      name: 'updateProfile',
      desc: '',
      args: [],
    );
  }

  /// `Profile updated successfully`
  String get profileUpdatedSuccessfully {
    return Intl.message(
      'Profile updated successfully',
      name: 'profileUpdatedSuccessfully',
      desc: '',
      args: [],
    );
  }

  /// `Leaves`
  String get leaves {
    return Intl.message('Leaves', name: 'leaves', desc: '', args: []);
  }

  /// `Leave Requests`
  String get leaveRequests {
    return Intl.message(
      'Leave Requests',
      name: 'leaveRequests',
      desc: '',
      args: [],
    );
  }

  /// `Add Leave Request`
  String get addLeaveRequest {
    return Intl.message(
      'Add Leave Request',
      name: 'addLeaveRequest',
      desc: '',
      args: [],
    );
  }

  /// `Edit Leave Request`
  String get editLeaveRequest {
    return Intl.message(
      'Edit Leave Request',
      name: 'editLeaveRequest',
      desc: '',
      args: [],
    );
  }

  /// `Leave Type`
  String get leaveType {
    return Intl.message('Leave Type', name: 'leaveType', desc: '', args: []);
  }

  /// `From Date`
  String get fromDate {
    return Intl.message('From Date', name: 'fromDate', desc: '', args: []);
  }

  /// `To Date`
  String get toDate {
    return Intl.message('To Date', name: 'toDate', desc: '', args: []);
  }

  /// `Reason`
  String get reason {
    return Intl.message('Reason', name: 'reason', desc: '', args: []);
  }

  /// `Vacation Leave`
  String get vacationLeave {
    return Intl.message(
      'Vacation Leave',
      name: 'vacationLeave',
      desc: '',
      args: [],
    );
  }

  /// `Sick Leave`
  String get sickLeave {
    return Intl.message('Sick Leave', name: 'sickLeave', desc: '', args: []);
  }

  /// `Request Leave`
  String get requestLeave {
    return Intl.message(
      'Request Leave',
      name: 'requestLeave',
      desc: '',
      args: [],
    );
  }

  /// `Leave request added successfully`
  String get leaveRequestAdded {
    return Intl.message(
      'Leave request added successfully',
      name: 'leaveRequestAdded',
      desc: '',
      args: [],
    );
  }

  /// `Leave request updated successfully`
  String get leaveRequestUpdated {
    return Intl.message(
      'Leave request updated successfully',
      name: 'leaveRequestUpdated',
      desc: '',
      args: [],
    );
  }

  /// `Leave request deleted successfully`
  String get leaveRequestDeleted {
    return Intl.message(
      'Leave request deleted successfully',
      name: 'leaveRequestDeleted',
      desc: '',
      args: [],
    );
  }

  /// `Delete Leave Request`
  String get deleteLeaveRequest {
    return Intl.message(
      'Delete Leave Request',
      name: 'deleteLeaveRequest',
      desc: '',
      args: [],
    );
  }

  /// `Are you sure you want to delete this leave request?`
  String get areYouSureDeleteLeave {
    return Intl.message(
      'Are you sure you want to delete this leave request?',
      name: 'areYouSureDeleteLeave',
      desc: '',
      args: [],
    );
  }

  /// `Clients`
  String get clients {
    return Intl.message('Clients', name: 'clients', desc: '', args: []);
  }

  /// `Add Client`
  String get addClient {
    return Intl.message('Add Client', name: 'addClient', desc: '', args: []);
  }

  /// `Company Name`
  String get companyName {
    return Intl.message(
      'Company Name',
      name: 'companyName',
      desc: '',
      args: [],
    );
  }

  /// `Responsible Name`
  String get responsibleName {
    return Intl.message(
      'Responsible Name',
      name: 'responsibleName',
      desc: '',
      args: [],
    );
  }

  /// `Responsible Phone`
  String get responsiblePhone {
    return Intl.message(
      'Responsible Phone',
      name: 'responsiblePhone',
      desc: '',
      args: [],
    );
  }

  /// `Responsible Job`
  String get responsibleJob {
    return Intl.message(
      'Responsible Job',
      name: 'responsibleJob',
      desc: '',
      args: [],
    );
  }

  /// `Responsible Email`
  String get responsibleEmail {
    return Intl.message(
      'Responsible Email',
      name: 'responsibleEmail',
      desc: '',
      args: [],
    );
  }

  /// `Client Status`
  String get clientStatus {
    return Intl.message(
      'Client Status',
      name: 'clientStatus',
      desc: '',
      args: [],
    );
  }

  /// `Leads`
  String get leads {
    return Intl.message('Leads', name: 'leads', desc: '', args: []);
  }

  /// `Client added successfully`
  String get clientAdded {
    return Intl.message(
      'Client added successfully',
      name: 'clientAdded',
      desc: '',
      args: [],
    );
  }

  /// `Approved`
  String get approved {
    return Intl.message('Approved', name: 'approved', desc: '', args: []);
  }

  /// `Pending`
  String get pending {
    return Intl.message('Pending', name: 'pending', desc: '', args: []);
  }

  /// `Rejected`
  String get rejected {
    return Intl.message('Rejected', name: 'rejected', desc: '', args: []);
  }

  /// `Issued At`
  String get issuedAt {
    return Intl.message('Issued At', name: 'issuedAt', desc: '', args: []);
  }

  /// `Select Date`
  String get selectDate {
    return Intl.message('Select Date', name: 'selectDate', desc: '', args: []);
  }

  /// `Select Leave Type`
  String get selectLeaveType {
    return Intl.message(
      'Select Leave Type',
      name: 'selectLeaveType',
      desc: '',
      args: [],
    );
  }

  /// `Reason cannot be empty`
  String get reasonCannotBeEmpty {
    return Intl.message(
      'Reason cannot be empty',
      name: 'reasonCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `From date cannot be empty`
  String get fromDateCannotBeEmpty {
    return Intl.message(
      'From date cannot be empty',
      name: 'fromDateCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `To date cannot be empty`
  String get toDateCannotBeEmpty {
    return Intl.message(
      'To date cannot be empty',
      name: 'toDateCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Company name cannot be empty`
  String get companyNameCannotBeEmpty {
    return Intl.message(
      'Company name cannot be empty',
      name: 'companyNameCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Responsible name cannot be empty`
  String get responsibleNameCannotBeEmpty {
    return Intl.message(
      'Responsible name cannot be empty',
      name: 'responsibleNameCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Responsible phone cannot be empty`
  String get responsiblePhoneCannotBeEmpty {
    return Intl.message(
      'Responsible phone cannot be empty',
      name: 'responsiblePhoneCannotBeEmpty',
      desc: '',
      args: [],
    );
  }

  /// `Tickets`
  String get tickets {
    return Intl.message('Tickets', name: 'tickets', desc: '', args: []);
  }

  /// `Contracts`
  String get contracts {
    return Intl.message('Contracts', name: 'contracts', desc: '', args: []);
  }

  /// `Near Expire`
  String get nearExpire {
    return Intl.message('Near Expire', name: 'nearExpire', desc: '', args: []);
  }

  /// `Expired`
  String get expired {
    return Intl.message('Expired', name: 'expired', desc: '', args: []);
  }
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'en'),
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
